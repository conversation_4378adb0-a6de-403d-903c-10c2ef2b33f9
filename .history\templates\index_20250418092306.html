<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NMR预测系统</title>
    <!-- 更新SmilesDrawer库的CDN链接 -->
    <script src="https://unpkg.com/smiles-drawer@2.0.1/dist/smiles-drawer.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        textarea {
            width: 100%;
            height: 100px;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-family: monospace;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        .loading {
            display: none;
            margin-top: 15px;
        }
        .results {
            margin-top: 20px;
            display: none;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
        .error {
            color: #f44336;
            margin-top: 15px;
            display: none;
        }
        .info {
            margin-top: 10px;
            font-style: italic;
            color: #666;
        }
        .structure-canvas {
            width: 200px;
            height: 150px;
        }
        .formula {
            font-family: "Times New Roman", Times, serif;
            font-size: 14px;
            margin-bottom: 5px;
            color: #333;
        }
        .smiles-text {
            font-family: monospace;
            font-size: 12px;
            color: #666;
            margin-top: 5px;
            word-break: break-all;
        }
        .structure-container {
            display: flex;
            flex-direction: column;
        }
        .example-input {
            margin-top: 5px;
            color: #4CAF50;
            cursor: pointer;
            font-size: 13px;
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>NMR预测系统</h1>
        <p>输入NMR值列表，系统将预测最可能的化学结构</p>
        
        <div class="form-group">
            <label for="nmr-input">NMR值 (每个值用逗号分隔):</label>
            <textarea id="nmr-input" placeholder="例如: 173.69, 136.21, 128.55, 128.17, 128.16, 66.06"></textarea>
            <div class="info">可接受数字、逗号、空格和换行符</div>
            <div class="example-input" onclick="useExample('170.31, 63.73, 30.67, 20.55, 19.01, 13.55')">示例1: 170.31, 63.73, 30.67, 20.55, 19.01, 13.55</div>
            <div class="example-input" onclick="useExample('173.69, 136.21, 128.55, 128.17, 128.16, 66.06, 34.32, 31.31, 24.67, 22.31, 13.89')">示例2: 173.69, 136.21, 128.55, 128.17...</div>
            <div class="example-input" onclick="useExample('137.50, 115.03, 63.50')">示例3: 137.50, 115.03, 63.50</div>
        </div>
        
        <button id="predict-btn">预测结果</button>
        
        <div id="loading" class="loading">
            <p>正在处理，请稍候...</p>
        </div>
        
        <div id="error" class="error"></div>
        
        <div id="results" class="results">
            <h2>预测结果</h2>
            <p>输入的NMR值: <span id="input-nmr"></span></p>
            <p>搜索用时: <span id="search-time"></span> 秒</p>
            <table>
                <thead>
                    <tr>
                        <th>排名</th>
                        <th>分数</th>
                        <th>化学结构</th>
                    </tr>
                </thead>
                <tbody id="results-table">
                    <!-- 结果会动态添加到这里 -->
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // 示例输入函数
        function useExample(example) {
            document.getElementById('nmr-input').value = example;
        }
        
        // 确认SmilesDrawer库已加载
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof SmilesDrawer === 'undefined') {
                console.error('SmilesDrawer库未能正确加载！');
                showError('无法加载化学结构渲染库，请刷新页面重试。');
            } else {
                console.log('SmilesDrawer库已成功加载。');
            }
        });
        
        // SmilesDrawer配置
        const smilesDrawerOptions = {
            width: 200,
            height: 150,
            bondThickness: 1.2,
            bondLength: 20,
            shortBondLength: 0.8,
            bondSpacing: 5.1,
            atomVisualization: 'default',
            isomeric: true,
            debug: false,
            terminalCarbons: true,
            explicitHydrogens: false,  // 设为false可以简化结构显示
            overlapSensitivity: 0.42,
            overlapResolutionIterations: 1,
            compactDrawing: true
        };
        
        document.getElementById('predict-btn').addEventListener('click', async function() {
            // 获取输入值
            const inputText = document.getElementById('nmr-input').value.trim();
            
            if (!inputText) {
                showError('请输入NMR值');
                return;
            }
            
            // 解析输入，支持多种分隔符
            const cleanedInput = inputText.replace(/\s+/g, ',').replace(/,+/g, ',').replace(/^,|,$/g, '');
            const nmrValues = cleanedInput.split(',').filter(val => val.trim() !== '');
            
            if (nmrValues.length === 0) {
                showError('无法解析输入值');
                return;
            }
            
            // 显示加载状态
            document.getElementById('loading').style.display = 'block';
            document.getElementById('error').style.display = 'none';
            document.getElementById('results').style.display = 'none';
            
            try {
                // 发送预测请求
                const response = await fetch('/predict', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ nmr_values: nmrValues })
                });
                
                const data = await response.json();
                
                // 隐藏加载状态
                document.getElementById('loading').style.display = 'none';
                
                if (data.error) {
                    showError(data.error);
                    return;
                }
                
                // 显示结果
                document.getElementById('input-nmr').textContent = data.input_nmr.join(', ');
                document.getElementById('search-time').textContent = data.search_time.toFixed(2);
                
                const resultsTable = document.getElementById('results-table');
                resultsTable.innerHTML = '';
                
                // 验证SmilesDrawer是否已加载
                if (typeof SmilesDrawer === 'undefined') {
                    showError('化学结构渲染库未能正确加载，将只显示SMILES字符串和分子式。');
                    // 仍然显示结果，但不渲染结构
                    data.results.forEach(result => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${result.rank}</td>
                            <td>${result.score.toFixed(4)}</td>
                            <td>
                                <div class="formula">${result.formula}</div>
                                <div class="smiles-text">${result.smiles}</div>
                            </td>
                        `;
                        resultsTable.appendChild(row);
                    });
                    document.getElementById('results').style.display = 'block';
                    return;
                }
                
                // 渲染每个结果
                data.results.forEach(result => {
                    const row = document.createElement('tr');
                    
                    // 创建单元格
                    const rankCell = document.createElement('td');
                    rankCell.textContent = result.rank;
                    
                    const scoreCell = document.createElement('td');
                    scoreCell.textContent = result.score.toFixed(4);
                    
                    const structureCell = document.createElement('td');
                    const structureContainer = document.createElement('div');
                    structureContainer.className = 'structure-container';
                    
                    // 添加分子式
                    const formulaDiv = document.createElement('div');
                    formulaDiv.className = 'formula';
                    formulaDiv.innerHTML = result.formula; // 使用innerHTML以支持<sub>标签
                    
                    // 创建画布
                    const canvas = document.createElement('canvas');
                    canvas.className = 'structure-canvas';
                    canvas.id = `structure-${result.rank}`;
                    
                    // 添加SMILES文本
                    const smilesText = document.createElement('div');
                    smilesText.className = 'smiles-text';
                    smilesText.textContent = result.smiles;
                    
                    // 构建DOM结构
                    structureContainer.appendChild(formulaDiv);
                    structureContainer.appendChild(canvas);
                    structureContainer.appendChild(smilesText);
                    structureCell.appendChild(structureContainer);
                    
                    // 添加单元格到行
                    row.appendChild(rankCell);
                    row.appendChild(scoreCell);
                    row.appendChild(structureCell);
                    
                    // 添加行到表格
                    resultsTable.appendChild(row);
                });
                
                document.getElementById('results').style.display = 'block';
                
                // 渲染所有化学结构
                data.results.forEach(result => {
                    try {
                        const drawer = new SmilesDrawer.Drawer(smilesDrawerOptions);
                        SmilesDrawer.parse(result.smiles, function(tree) {
                            drawer.draw(tree, `structure-${result.rank}`, 'light', false);
                        }, function(error) {
                            console.error('解析SMILES错误:', error);
                        });
                    } catch (e) {
                        console.error('渲染SMILES失败:', result.smiles, e);
                    }
                });
                
            } catch (error) {
                document.getElementById('loading').style.display = 'none';
                showError('发生错误: ' + error.message);
            }
        });
        
        function showError(message) {
            const errorDiv = document.getElementById('error');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }
    </script>
</body>
</html>