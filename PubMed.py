"""
PubMed NMR文献查询模块

提供简单的接口来查询PubMed中与化合物NMR相关的文献
"""

import requests
from xml.etree import ElementTree as ET
import time
import re

def search_pubmed_nmr(compound_name, email="<EMAIL>", max_results=10):
    """
    使用化合物名称在PubMed中搜索NMR相关文献（直接使用E-utilities API）

    参数:
        compound_name (str): 化合物的名称（IUPAC名称或通用名）
        email (str): 你的电子邮箱（遵守PubMed API要求）
        max_results (int): 最大返回结果数

    返回:
        list: 包含文献信息的字典列表
    """
    # PubMed E-utilities API端点
    ESEARCH_URL = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi"
    EFETCH_URL = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/efetch.fcgi"
    
    # 构建更精确的NMR查询
    nmr_terms = [
        "NMR", "nuclear magnetic resonance", 
        "13C NMR", "1H NMR", "31P NMR",
        "2D NMR", "COSY", "NOESY", "HSQC", "HMBC",
        "solid state NMR", "solution NMR", "chemical shift"
    ]
    nmr_query = " OR ".join([f'"{term}"[Title/Abstract]' for term in nmr_terms])
    
    query = f'("{compound_name}"[Title/Abstract]) AND ({nmr_query})'
    
    try:
        # 第一步：搜索匹配的PubMed ID
        search_params = {
            "db": "pubmed",
            "term": query,
            "retmax": max_results,
            "sort": "relevance",
            "email": email,
            "tool": "PythonNMRQuery"
        }
        
        response = requests.get(ESEARCH_URL, params=search_params)
        response.raise_for_status()  # 检查HTTP错误
        
        # 解析XML响应
        root = ET.fromstring(response.content)
        id_elements = root.findall(".//Id")
        
        if not id_elements:
            return []
        
        id_list = [id_elem.text for id_elem in id_elements]
        
        # 第二步：获取文献详细信息
        fetch_params = {
            "db": "pubmed",
            "id": ",".join(id_list),
            "retmode": "xml",
            "email": email
        }
        
        response = requests.get(EFETCH_URL, params=fetch_params)
        response.raise_for_status()
        
        # 解析XML文献数据
        root = ET.fromstring(response.content)
        results = []
        
        for article in root.findall(".//PubmedArticle"):
            # 提取PMID
            pmid = article.find(".//PMID").text
            
            # 提取标题
            title_elem = article.find(".//ArticleTitle")
            title = title_elem.text if title_elem is not None else "No Title"
            
            # 提取摘要（处理多部分摘要）
            abstract = ""
            abstract_elems = article.findall(".//AbstractText")
            if abstract_elems:
                for elem in abstract_elems:
                    if elem.text:
                        abstract += elem.text + " "
                abstract = abstract.strip()
            else:
                abstract = "No abstract available"
            
            # 提取作者列表
            authors = []
            author_list = article.findall(".//Author")
            for author in author_list:
                last_name = author.find("LastName")
                fore_name = author.find("ForeName")
                initials = author.find("Initials")
                
                if last_name is not None and last_name.text:
                    name = last_name.text
                    if fore_name is not None and fore_name.text:
                        name += f", {fore_name.text}"
                    elif initials is not None and initials.text:
                        name += f", {initials.text}"
                    authors.append(name)
            
            # 提取期刊信息
            journal_elem = article.find(".//Journal/Title")
            journal = journal_elem.text if journal_elem is not None else "Unknown Journal"
            
            # 提取发表日期
            pub_date = article.find(".//PubDate")
            year = pub_date.find("Year").text if pub_date is not None and pub_date.find("Year") is not None else "Unknown Year"
            
            # 提取DOI（如果存在）
            doi = "Not available"
            article_id_elems = article.findall(".//ArticleId")
            for id_elem in article_id_elems:
                if id_elem.attrib.get("IdType", "") == "doi":
                    doi = id_elem.text
                    break
            
            results.append({
                "PMID": pmid,
                "DOI": doi,
                "Title": title,
                "Abstract": abstract,
                "Authors": authors,
                "Journal": journal,
                "Year": year,
                "Query": query
            })
        
        return results
    
    except requests.exceptions.RequestException as e:
        print(f"API请求出错: {e}")
        return []
    except ET.ParseError as e:
        print(f"XML解析出错: {e}")
        return []
    except Exception as e:
        print(f"处理出错: {e}")
        return []

def simplify_compound_name(compound_name):
    """
    简化复杂的化合物名称以提高搜索效果
    """
    # 移除立体化学描述符
    simplified = re.sub(r'\([^)]*\)', '', compound_name)

    # 移除数字上标
    simplified = re.sub(r'\^\{[^}]*\}', '', simplified)

    # 移除特殊字符
    simplified = re.sub(r'[\[\]{}]', '', simplified)

    # 标准化空格
    simplified = ' '.join(simplified.split())

    return simplified


def query_nmr_literature(compound_name, max_results=10):
    """
    简化的接口函数：根据化合物名称查询NMR文献

    参数:
        compound_name (str): 化合物名称
        max_results (int): 最大返回结果数，默认10

    返回:
        list: 包含文献信息的字典列表，每个字典包含：
            - PMID: PubMed ID
            - DOI: 数字对象标识符
            - Title: 文章标题
            - Abstract: 摘要
            - Authors: <AUTHORS>
            - Journal: 期刊名称
            - Year: 发表年份
            - Query: 使用的查询语句
    """
    # 简化化合物名称
    simplified_name = simplify_compound_name(compound_name)

    # 调用主查询函数
    results = search_pubmed_nmr(simplified_name, max_results=max_results)

    return results

# 使用示例
if __name__ == "__main__":
    # 示例化合物名称
    compound_name = "benzene"

    print(f"正在查询化合物 '{compound_name}' 的NMR文献...")
    start_time = time.time()

    # 使用简化接口查询
    results = query_nmr_literature(compound_name, max_results=5)
    elapsed = time.time() - start_time

    if results:
        print(f"\n找到 {len(results)} 篇相关文献 (耗时: {elapsed:.2f}秒)")
        print(f"使用的查询语句: {results[0]['Query']}\n")

        for i, paper in enumerate(results, 1):
            print(f"{i}. [{paper['Year']}] {paper['Title']}")
            print(f"   PMID: {paper['PMID']} | DOI: {paper['DOI']}")
            print(f"   作者: {', '.join(paper['Authors'][:3])}{' et al.' if len(paper['Authors']) > 3 else ''}")
            print(f"   期刊: {paper['Journal']}")
            print(f"   摘要: {paper['Abstract'][:200]}...\n")
    else:
        print("未找到相关文献")