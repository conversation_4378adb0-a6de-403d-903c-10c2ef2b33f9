<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CIQTEK - NMR Prediction System</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #4361ee;
            --primary-hover: #3a56d4;
            --secondary-color: #3f37c9;
            --accent-color: #4cc9f0;
            --light-bg: #f8f9fa;
            --dark-text: #333;
            --light-text: #f8f9fa;
            --border-radius: 8px;
            --box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --transition: all 0.3s ease;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "PingFang SC", "Microsoft YaHei", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol";
            max-width: 1000px;
            margin: 0 auto;
            padding: 30px 20px;
            line-height: 1.6;
            color: var(--dark-text);
            background-color: #fdfdfd;
        }
        
        .container {
            background-color: white;
            border-radius: var(--border-radius);
            padding: 30px;
            box-shadow: var(--box-shadow);
        }
        
        h1 {
            color: var(--primary-color);
            margin-bottom: 10px;
            font-weight: 600;
            font-size: 28px;
        }
        
        p.description {
            color: #666;
            margin-bottom: 25px;
            font-size: 16px;
        }
        
        .form-group {
            margin-bottom: 25px;
            background-color: var(--light-bg);
            padding: 20px;
            border-radius: var(--border-radius);
            border-left: 4px solid var(--primary-color);
        }
        
        label {
            display: block;
            margin-bottom: 10px;
            font-weight: 600;
            color: var(--secondary-color);
            font-size: 16px;
        }
        
        textarea {
            width: 100%;
            height: 120px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            font-family: monospace;
            font-size: 15px;
            transition: var(--transition);
            resize: vertical;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        textarea:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(76, 201, 240, 0.3);
        }
        
        button {
            background-color: var(--primary-color);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: var(--border-radius);
            /* cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            transition: var(--transition); */
            box-shadow: 0 4px 6px rgba(67, 97, 238, 0.3);

            /* background: linear-gradient(135deg, #4cc9f0, #4361ee); */
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            /* box-shadow: 0 5px 15px rgba(67, 97, 238, 0.4); */
            display: flex
        ;
            align-items: center;
            gap: 10px;
            margin: 20px auto;
        }
        
        button:hover {
            background-color: var(--primary-hover);
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(67, 97, 238, 0.4);
        }
        
        button:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(67, 97, 238, 0.3);
        }
        
        /* Add styles for disabled button */
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
            opacity: 0.7;
        }
        
        .loading {
            display: none;
            margin: 30px 0;
            text-align: center;
        }
        
        .spinner {
            width: 70px;
            text-align: center;
            margin: 0 auto;
        }
        
        .spinner > div {
            width: 18px;
            height: 18px;
            background-color: var(--primary-color);
            border-radius: 100%;
            display: inline-block;
            animation: sk-bouncedelay 1.4s infinite ease-in-out both;
        }
        
        .spinner .bounce1 {
            animation-delay: -0.32s;
        }
        
        .spinner .bounce2 {
            animation-delay: -0.16s;
        }
        
        @keyframes sk-bouncedelay {
            0%, 80%, 100% { 
                transform: scale(0);
            } 40% { 
                transform: scale(1.0);
            }
        }
        
        .loading-text {
            margin-top: 15px;
            font-size: 16px;
            color: var(--primary-color);
            font-weight: 500;
        }
        
        .progress-bar {
            height: 4px;
            width: 100%;
            background-color: #e9ecef;
            border-radius: 2px;
            margin-top: 15px;
            overflow: hidden;
        }
        
        .progress-bar-fill {
            height: 100%;
            background-color: var(--primary-color);
            border-radius: 2px;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .results {
            margin-top: 30px;
            display: none;
            animation: fadeIn 0.5s ease;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #eee;
        }
        
        .results-header h2 {
            color: var(--secondary-color);
            font-weight: 600;
            margin: 0;
        }
        
        .results-meta {
            display: flex;
            gap: 20px;
        }
        
        .result-meta-item {
            display: flex;
            flex-direction: column;
        }
        
        .meta-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 2px;
        }
        
        .meta-value {
            font-size: 14px;
            font-weight: 600;
            color: var(--dark-text);
        }
        
        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin-top: 10px;
            overflow: hidden;
            border-radius: var(--border-radius);
            box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
        }
        
        th, td {
            padding: 12px 15px;
            text-align: left;
        }
        
        th {
            background-color: var(--primary-color);
            color: white;
            font-weight: 500;
            text-transform: uppercase;
            font-size: 14px;
            letter-spacing: 1px;
        }
        
        th:first-child {
            border-top-left-radius: var(--border-radius);
        }
        
        th:last-child {
            border-top-right-radius: var(--border-radius);
        }
        
        tr:nth-child(even) {
            background-color: #f2f8ff;
        }
        
        tr:hover {
            background-color: #e6f3ff;
        }
        
        .error {
            color: #e63946;
            margin-top: 15px;
            padding: 15px;
            border-radius: var(--border-radius);
            background-color: #fde8e8;
            border-left: 4px solid #e63946;
            font-weight: 500;
            display: none;
        }
        
        .info {
            margin-top: 10px;
            font-style: italic;
            color: #666;
            font-size: 14px;
        }
        
        .structure-img {
            width: 180px;
            height: 150px;
            background-color: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border-radius: 4px;
            margin: 10px 0;
            object-fit: contain;
        }
        
        .formula {
            font-family: "Times New Roman", Times, serif;
            font-size: 16px;
            color: var(--secondary-color);
            font-weight: 600;
            padding: 8px 0;
        }
        
        .name {
            font-weight: 600;
            color: var(--dark-text);
            margin-bottom: 5px;
        }
        
        .mol-weight {
            font-size: 14px;
            color: #666;
        }
        
        .structure-container {
            display: flex;
            flex-direction: column;
            min-width: 200px;
        }
        
        .example-section {
            margin-top: 15px;
        }
        
        .examples-header {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }
        
        .examples-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .example-input {
            background-color: #eef2ff;
            padding: 8px 12px;
            border-radius: 16px;
            color: var(--primary-color);
            cursor: pointer;
            font-size: 14px;
            transition: var(--transition);
            border: 1px solid #d8e3fd;
        }
        
        .example-input:hover {
            background-color: #d8e3fd;
            transform: translateY(-2px);
        }
        
        .btn-container {
            display: flex;
            justify-content: flex-start;
        }
        
        /* Responsive design */
        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }

            .results-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .results-meta {
                margin-top: 15px;
                flex-wrap: wrap;
            }

            .structure-img {
                width: 150px;
                height: 120px;
            }
        }

        /* Watermark styles */
        .watermark {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 9999;
            display: flex;
            flex-wrap: wrap;
            overflow: hidden;
            opacity: 0.1;
            user-select: none;
        }

        .watermark-item {
            font-size: 48px;
            color: #000;
            transform: rotate(-30deg);
            white-space: nowrap;
            padding: 100px;
            font-family: Arial, sans-serif;
            font-weight: bold;
        }

        /* Footer styles */
        .footer {
            margin-top: 50px;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 14px;
            line-height: 1.6;
        }

        .footer p {
            margin: 5px 0;
        }

        .footer .company-name {
            font-weight: 600;
            color: var(--primary-color);
        }

        /* Right side panel styles */
        .side-panel {
            position: fixed;
            top: 0;
            right: -500px;
            width: 500px;
            height: 100vh;
            background-color: white;
            box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
            transition: right 0.3s ease;
            z-index: 10000;
            overflow-y: auto;
            padding: 20px;
        }

        .side-panel.open {
            right: 0;
        }

        .side-panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #eee;
        }

        .side-panel-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--primary-color);
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
            padding: 5px;
            border-radius: 4px;
            transition: var(--transition);
        }

        .close-btn:hover {
            background-color: #f0f0f0;
            color: var(--primary-color);
        }

        .detail-section {
            margin-bottom: 20px;
        }

        .detail-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .detail-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(180deg, var(--primary-color), var(--accent-color));
        }

        .detail-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
        }

        .detail-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        .detail-icon {
            width: 24px;
            height: 24px;
            margin-right: 10px;
            color: var(--primary-color);
        }

        .detail-label {
            font-weight: 600;
            color: var(--secondary-color);
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin: 0;
        }

        .detail-value {
            color: var(--dark-text);
            font-size: 15px;
            line-height: 1.6;
            word-break: break-word;
            margin: 0;
        }

        .detail-value.large {
            font-size: 18px;
            font-weight: 600;
            color: var(--primary-color);
        }

        .detail-value.code {
            font-family: 'Courier New', monospace;
            background-color: #f1f3f4;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 13px;
            margin-top: 8px;
        }

        .compound-title {
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            color: white;
            padding: 24px;
            border-radius: 12px;
            margin-bottom: 20px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(67, 97, 238, 0.3);
        }

        .compound-name {
            font-size: 22px;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .compound-formula {
            font-size: 16px;
            opacity: 0.9;
            font-family: 'Times New Roman', serif;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 20px;
        }

        .info-item {
            background: white;
            padding: 16px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            text-align: center;
            transition: all 0.3s ease;
        }

        .info-item:hover {
            border-color: var(--accent-color);
            transform: translateY(-1px);
        }

        .info-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 6px;
        }

        .info-value {
            font-size: 16px;
            font-weight: 600;
            color: var(--primary-color);
        }

        .loading-detail {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .loading-detail .spinner {
            width: 50px;
            margin: 0 auto 20px;
        }

        .loading-detail .spinner > div {
            width: 12px;
            height: 12px;
            background-color: var(--primary-color);
            border-radius: 100%;
            display: inline-block;
            animation: sk-bouncedelay 1.4s infinite ease-in-out both;
            margin: 0 3px;
        }

        .loading-message {
            font-size: 16px;
            color: var(--primary-color);
            font-weight: 500;
            margin-bottom: 10px;
        }

        .loading-submessage {
            font-size: 14px;
            color: #888;
        }

        .error-detail {
            background-color: #fde8e8;
            color: #e63946;
            padding: 15px;
            border-radius: var(--border-radius);
            border-left: 4px solid #e63946;
            margin: 20px 0;
        }

        /* Overlay */
        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .overlay.show {
            opacity: 1;
            visibility: visible;
        }
    </style>
</head>
<body>
    <!-- Add watermark container -->
    <div class="watermark" id="watermark"></div>
    <div class="container">
        <!-- <h1>CIQTEK - NMR AI Prediction System</h1> -->
        <h1><i class="fas fa-atom"></i> AI NMR Molecular Structure Prediction</h1>
        <p class="description">Enter the ppm list of NMR C spectrum, and the system will predict the most likely chemical structure to assist your research analysis</p>
        
        <div class="form-group">
            <label for="nmr-input">PPM (separate each value with comma):</label>
            <textarea id="nmr-input" placeholder="e.g.: 171.03 70.66 27.83 20.85 19.12	"></textarea>
            <div class="info">Accepts numbers, commas, spaces and line breaks</div>

            <div class="example-section">
                <div class="examples-header">Quick Examples:</div>
                <div class="examples-container">
                    <div class="example-input" onclick="useExample('171.03 70.66 27.83 20.85 19.12	')">Example 1: isobutyl acetate</div>
                    <div class="example-input" onclick="useExample('166.50 132.70 130.62 129.53 128.27 69.87 35.79 32.53 20.05 17.03 14.30')">Example 2: 2-Methylpentyl benzoate</div>
                    <div class="example-input" onclick="useExample('137.50, 115.03, 63.50')">Example 3: Allyl alcohol</div>
                    <div class="example-input" onclick="useExample('57.79,18.13')">Example 4: ethyl alcohol</div>
                </div>
            </div>
        </div>
        
        <div class="btn-container">
            <button id="predict-btn"><i class="fas fa-brain ai-icon"></i> Predict Molecular Structure</button>
            <!-- <button id="deep-thinking" disabled><i class="fas fa-brain ai-atom"></i> Deep Thinking (Coming Soon)</button> -->
        </div>
        
        <div id="loading" class="loading">
            <div class="spinner">
                <div class="bounce1"></div>
                <div class="bounce2"></div>
                <div class="bounce3"></div>
            </div>
            <p class="loading-text">Analyzing NMR data, please wait...</p>
            <div class="progress-bar">
                <div class="progress-bar-fill" id="progress-fill"></div>
            </div>
        </div>
        
        <div id="error" class="error"></div>
        
        <div id="results" class="results">
            <div class="results-header">
                <h2>Prediction Results</h2>
                <div class="results-meta">
                    <div class="prediction-info">Note: The following results are automatically predicted by AI and are for reference only. Please verify carefully.</div>
                    <!-- <div class="result-meta-item">
                        <span class="meta-label">Input NMR Values</span>
                        <span class="meta-value" id="input-nmr"></span>
                    </div>
                    <div class="result-meta-item">
                        <span class="meta-label">Search Time</span>
                        <span class="meta-value"><span id="search-time"></span> seconds</span>
                    </div> -->
                </div>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>Rank</th>
                        <th>Match Score</th>
                        <th>Molecular Formula</th>
                        <th>Molecular Weight</th>  <!-- Added molecular weight column -->
                        <th>Chemical Structure</th>
                        <th>View</th>
                    </tr>
                </thead>
                <tbody id="results-table">
                    <!-- Results will be dynamically added here -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- Add footer -->
    <footer class="footer">
        <p><span class="company-name">CIQTEK</span> - Professional Scientific Instruments and Solutions Provider</p>
        <p>© <span id="current-year"></span> CIQTEK. All Rights Reserved.</p>
    </footer>

    <!-- Overlay -->
    <div class="overlay" id="overlay"></div>

    <!-- Right side panel -->
    <div class="side-panel" id="sidePanel">
        <div class="side-panel-header">
            <div class="side-panel-title">Molecular Details</div>
            <button class="close-btn" id="closePanelBtn">&times;</button>
        </div>

        <div id="panelContent">
            <!-- Content will be dynamically loaded -->
        </div>
    </div>

    <script>
        // Set current year
        document.getElementById('current-year').textContent = new Date().getFullYear();

        // Create watermark
        function createWatermark() {
            const watermarkContainer = document.getElementById('watermark');
            const windowWidth = window.innerWidth;
            const windowHeight = window.innerHeight;
            const text = 'CIQTEK';

            // Clear existing watermarks
            watermarkContainer.innerHTML = '';

            // Calculate how many watermark elements are needed to fill the screen
            const itemWidth = 300; // Approximate width of each watermark element
            const itemHeight = 200; // Approximate height of each watermark element
            const cols = Math.ceil(windowWidth / itemWidth) + 1;
            const rows = Math.ceil(windowHeight / itemHeight) + 1;

            // Create watermark grid
            for (let i = 0; i < rows; i++) {
                for (let j = 0; j < cols; j++) {
                    const watermarkItem = document.createElement('div');
                    watermarkItem.className = 'watermark-item';
                    watermarkItem.textContent = text;
                    watermarkContainer.appendChild(watermarkItem);
                }
            }
        }

        // Initially create watermark
        // createWatermark();

        // // Recreate watermark when window size changes
        // window.addEventListener('resize', createWatermark);

        // Example input function
        function useExample(example) {
            document.getElementById('nmr-input').value = example;
        }
        
        // Progress bar simulation function
        function simulateProgress() {
            const progressFill = document.getElementById('progress-fill');
            let width = 0;
            const duration = 10000; // 10 seconds
            const interval = 50; // Update interval (ms)
            const increment = 100 / (duration / interval);

            return new Promise((resolve) => {
                const timer = setInterval(() => {
                    if (width >= 95) {
                        clearInterval(timer);
                        setTimeout(() => {
                            progressFill.style.width = '100%';
                            setTimeout(resolve, 200);
                        }, 300);
                    } else {
                        width += increment;
                        // Add some randomness to the progress bar to make it look more natural
                        const randomFactor = 0.8 + Math.random() * 0.4;
                        const actualIncrement = increment * randomFactor;
                        width = Math.min(width + actualIncrement, 95);
                        progressFill.style.width = width + '%';
                    }
                }, interval);
            });
        }
        
        document.getElementById('predict-btn').addEventListener('click', async function() {
            // Get input value
            const inputText = document.getElementById('nmr-input').value.trim();

            if (!inputText) {
                showError('Please enter NMR values');
                return;
            }

            // Parse input, support multiple separators
            const cleanedInput = inputText.replace(/\s+/g, ',').replace(/,+/g, ',').replace(/^,|,$/g, '');
            const nmrValues = cleanedInput.split(',').filter(val => val.trim() !== '');

            if (nmrValues.length === 0) {
                showError('Unable to parse input values');
                return;
            }

            // Show loading state
            document.getElementById('loading').style.display = 'block';
            document.getElementById('error').style.display = 'none';
            document.getElementById('results').style.display = 'none';

            // Simulate progress bar - wait about 10 seconds
            const progressFill = document.getElementById('progress-fill');
            progressFill.style.width = '0%';
            await simulateProgress();
            
            try {
                // 发送预测请求
                const response = await fetch('/predict', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ nmr_values: nmrValues })
                });
                
                const data = await response.json();

                // Hide loading state
                document.getElementById('loading').style.display = 'none';

                if (data.error) {
                    showError(data.error);
                    return;
                }

                // Display results
                // document.getElementById('input-nmr').textContent = data.input_nmr.join(', ');
                // document.getElementById('search-time').textContent = data.search_time.toFixed(2);

                const resultsTable = document.getElementById('results-table');
                resultsTable.innerHTML = '';

                // Render each result
                data.results.forEach(result => {
                    const row = document.createElement('tr');

                    // Create cells
                    const rankCell = document.createElement('td');
                    rankCell.textContent = result.rank;

                    const scoreCell = document.createElement('td');
                    scoreCell.textContent = result.score.toFixed(4);

                    // Molecular formula cell
                    const formulaCell = document.createElement('td');
                    const formulaDiv = document.createElement('div');
                    formulaDiv.className = 'formula';
                    formulaDiv.innerHTML = result.formula;
                    formulaCell.appendChild(formulaDiv);

                    // Added molecular weight cell
                    const weightCell = document.createElement('td');
                    weightCell.className = 'mol-weight';
                    weightCell.textContent = result.mol_weight.toFixed(2);


                    // Chemical structure cell
                    const structureCell = document.createElement('td');
                    const structureContainer = document.createElement('div');
                    structureContainer.className = 'structure-container';

                    // View details button
                    const viewCell = document.createElement('td');
                    const viewButton = document.createElement('button');
                    viewButton.textContent = 'View Details';
                    viewButton.className = 'view-btn';
                    viewButton.style.cssText = `
                        background-color: var(--accent-color);
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 14px;
                        transition: var(--transition);
                        margin: 0;
                        display: inline-block;
                    `;

                    // Bind molecular formula to button click event
                    viewButton.addEventListener('click', function() {
                        openSidePanel(result.smiles);
                    });

                    // Add hover effect
                    viewButton.addEventListener('mouseenter', function() {
                        this.style.backgroundColor = '#3a9bc1';
                        this.style.transform = 'translateY(-1px)';
                    });

                    viewButton.addEventListener('mouseleave', function() {
                        this.style.backgroundColor = 'var(--accent-color)';
                        this.style.transform = 'translateY(0)';
                    });

                    viewCell.appendChild(viewButton);

                    // Create image element
                    const img = document.createElement('img');
                    img.className = 'structure-img';
                    img.src = 'data:image/png;base64,' + result.img_base64;
                    img.alt = result.name;

                    // Build DOM structure
                    structureContainer.appendChild(img);
                    structureCell.appendChild(structureContainer);

                    // Add cells to row
                    row.appendChild(rankCell);
                    row.appendChild(scoreCell);
                    row.appendChild(formulaCell);
                    row.appendChild(weightCell);  // Added molecular weight column
                    row.appendChild(structureCell);
                    row.appendChild(viewCell);

                    // Add row to table
                    resultsTable.appendChild(row);
                });

                document.getElementById('results').style.display = 'block';

            } catch (error) {
                document.getElementById('loading').style.display = 'none';
                showError('Error occurred: ' + error.message);
            }
        });
        
        function showError(message) {
            const errorDiv = document.getElementById('error');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        // Side panel related functions
        function openSidePanel(smiles) {
            const sidePanel = document.getElementById('sidePanel');
            const overlay = document.getElementById('overlay');
            const panelContent = document.getElementById('panelContent');

            // Show loading state
            panelContent.innerHTML = `
                <div class="loading-detail">
                    <div class="spinner">
                        <div class="bounce1"></div>
                        <div class="bounce2"></div>
                        <div class="bounce3"></div>
                    </div>
                    <div class="loading-message">Parsing detailed information</div>
                </div>
            `;

            // Show panel and overlay
            overlay.classList.add('show');
            sidePanel.classList.add('open');

            // Call PubChem API
            fetchMoleculeDetails(smiles);
        }

        function closeSidePanel() {
            const sidePanel = document.getElementById('sidePanel');
            const overlay = document.getElementById('overlay');

            overlay.classList.remove('show');
            sidePanel.classList.remove('open');
        }

        async function fetchMoleculeDetails(smiles) {
            const panelContent = document.getElementById('panelContent');

            try {
                // Assume moleculeData contains SMILES information, if not use molecular formula
                const apiUrl = `https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/smiles/${encodeURIComponent(smiles)}/property/Title,MolecularFormula,MolecularWeight,IUPACName,InChI,InChIKey,SMILES,MonoisotopicMass/JSON`;

                const response = await fetch(apiUrl);

                if (!response.ok) {
                    throw new Error('API request failed');
                }

                const data = await response.json();

                if (data.PropertyTable && data.PropertyTable.Properties && data.PropertyTable.Properties.length > 0) {
                    const compound = data.PropertyTable.Properties[0];
                    displayMoleculeDetails(compound);
                } else {
                    throw new Error('Compound information not found');
                }

            } catch (error) {
                panelContent.innerHTML = `
                    <div class="error-detail">
                        <i class="fas fa-exclamation-triangle" style="margin-right: 8px;"></i>
                        <strong>Unable to retrieve online detailed information</strong><br><br>
                        <div style="color: #666; font-size: 14px; margin-bottom: 20px;">
                            ${error.message}
                        </div>
                    </div>



                    <div style="margin-top: 20px; padding: 16px; background-color: #fff3cd; border-radius: 8px; border-left: 4px solid #ffc107; color: #856404; font-size: 14px;">
                        <i class="fas fa-info-circle" style="margin-right: 6px;"></i>
                        Note: Network connection issue or compound does not exist in PubChem database
                    </div>
                `;
            }
        }

        function displayMoleculeDetails(compound) {
            const panelContent = document.getElementById('panelContent');

            panelContent.innerHTML = `

                <!-- IUPAC Name Card -->
                ${compound.IUPACName ? `
                <div class="detail-card">
                    <div class="detail-header">
                        <i class="fas fa-tag detail-icon"></i>
                        <div class="detail-label">IUPAC Name</div>
                    </div>
                    <div class="detail-value">${compound.IUPACName}</div>
                </div>
                ` : ''}


                                <!-- Title Card -->
                ${compound.Title ? `
                <div class="detail-card">
                    <div class="detail-header">
                        <i class="fas fa-tag detail-icon"></i>
                        <div class="detail-label">Name</div>
                    </div>
                    <div class="detail-value">${compound.Title}</div>
                </div>
                ` : ''}

                <!-- SMILES Structure Card -->
                ${compound.SMILES ? `
                <div class="detail-card">
                    <div class="detail-header">
                        <i class="fas fa-project-diagram detail-icon"></i>
                        <div class="detail-label">SMILES Structure</div>
                    </div>
                    <div class="detail-value code">${compound.SMILES}</div>
                </div>
                ` : ''}

                <!-- InChI Identifier Card -->
                ${compound.InChI ? `
                <div class="detail-card">
                    <div class="detail-header">
                        <i class="fas fa-fingerprint detail-icon"></i>
                        <div class="detail-label">InChI Identifier</div>
                    </div>
                    <div class="detail-value code">${compound.InChI}</div>
                </div>
                ` : ''}

                <!-- InChI Key Card -->
                ${compound.InChIKey ? `
                <div class="detail-card">
                    <div class="detail-header">
                        <i class="fas fa-key detail-icon"></i>
                        <div class="detail-label">InChI Key</div>
                    </div>
                    <div class="detail-value code">${compound.InChIKey}</div>
                </div>
                ` : ''}

                <!-- Mass Information Card -->
                ${compound.MonoisotopicMass ? `
                <div class="detail-card">
                    <div class="detail-header">
                        <i class="fas fa-weight detail-icon"></i>
                        <div class="detail-label">Monoisotopic Mass</div>
                    </div>
                    <div class="detail-value">${parseFloat(compound.MonoisotopicMass).toFixed(6)} Da</div>
                </div>
                ` : ''}

            `;
        }

        // Add event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Close button event
            document.getElementById('closePanelBtn').addEventListener('click', closeSidePanel);

            // Overlay click event
            document.getElementById('overlay').addEventListener('click', closeSidePanel);

            // ESC key to close panel
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeSidePanel();
                }
            });
        });
    </script>
</body>
</html>