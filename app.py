from flask import Flask, request, render_template, jsonify
import torch
import json
from tqdm import tqdm
import os
import time
import faiss
from infer import ModelInference
import re
from rdkit import Chem
from rdkit.Chem import rdMolDescriptors

from rdkit.Chem import Descriptors, Draw
from rdkit.Chem.rdMolDescriptors import CalcMolFormula
import io
import base64
import requests

from PubMed import query_nmr_literature





app = Flask(__name__)

# 全局变量保存模型和特征，避免重复加载
model_inference = None
smiles_feature = None
smiles_list = None

def get_topK_result_faiss(nmr_feature, smiles_feature, topK):
    # 将特征转换为合适的格式
    smiles_feature_np = smiles_feature.cpu().numpy()
    nmr_feature_np = nmr_feature.cpu().numpy()
    
    # 创建FAISS索引
    dimension = smiles_feature_np.shape[1]
    index = faiss.IndexFlatIP(dimension)  # 使用内积相似度
    index.add(smiles_feature_np)
    
    # 搜索
    scores, indices = index.search(nmr_feature_np, topK)
    
    return torch.tensor(indices), torch.tensor(scores)

def get_or_compute_features(json_list, model_inference, cache_file="feature_cache.pt"):
    if os.path.exists(cache_file):
        print(f"Loading cached features from {cache_file}")
        return torch.load(cache_file)
    
    # 原有的特征提取代码
    smiles_feature, smiles_list = get_feature_from_json(
        json_list=json_list,
        model_inference=model_inference,
        n=256,  # 增大批量大小
        save_name=None,
        type="smiles",
        flag_get_value=True)
    
    # 缓存结果
    torch.save((smiles_feature, smiles_list), cache_file)
    return smiles_feature, smiles_list

def get_feature_from_json(json_list,
                          save_name,
                          model_inference,
                          n=512,
                          type="nmr",
                          flag_get_value=False):
    context = []
    print("start parse json")
    for file in json_list:
        with open(file, "r") as f:
            context_tmp = json.loads(f.read())
            context_tmp = [
                i[type][0] for i in tqdm(context_tmp) if len(i[type]) > 0
            ]
        context += context_tmp
    print("Size of the library: ", len(context))
    if flag_get_value == "only":
        return context
    if type == "nmr":
        fn = model_inference.nmr_encode
    elif type == "smiles":
        fn = model_inference.smiles_encode
    contexts = []
    print("start load batch")
    for i in range(0, len(context), n):
        contexts.append(context[i:i + n])
    print("start encode batch")
    result = [fn(i).cpu() for i in tqdm(contexts)]
    result = torch.cat(result, 0)
    if flag_get_value is True:
        if save_name is not None:
            torch.save((result, context), save_name)
        return result, context

    if save_name is not None:
        torch.save(result, save_name)
    return result

def smiles_to_molecular_formula(smiles):
    print("输入的SMILES:", smiles)
    """将SMILES转换为分子式"""
    try:
        mol = Chem.MolFromSmiles(smiles)
        if mol is None:
            return "转换失败"
        
        # 获取分子式
        formula = rdMolDescriptors.CalcMolFormula(mol)
        
        # 转换为带下标的形式 (HTML格式)
        formula_html = re.sub(r'(\d+)', r'<sub>\1</sub>', formula)
        
        return formula_html
    except Exception as e:
        print(f"转换SMILES到分子式时出错: {e}")
        return "转换失败"

def get_iupac_from_pubchem(smiles):
    url = f"https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/smiles/{smiles}/property/IUPACName/JSON"
    response = requests.get(url, timeout=10, verify=False)  # 不建议生产环境长用 verify=False
    if response.ok:
        try:
            data = response.json()
            name = data['PropertyTable']['Properties'][0].get('IUPACName', '名称不可用')
            return name
        except Exception as e:
            print("解析JSON失败：", e)
            return "解析失败"
    else:
        return "名称不可用"


def init_model():
    """初始化模型和特征，在第一次请求时调用"""
    global model_inference, smiles_feature, smiles_list
    
    if model_inference is None:
        # 设置环境变量
        os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"
        
        # 加载模型
        config_path = "models/2_5_w_model/8.json"
        pretrain_model_path = "models/2_5_w_model/8.pth"
        model_inference = ModelInference(config_path=config_path,
                                         pretrain_model_path=pretrain_model_path,
                                         device="cuda")
        
        # 加载特征
        cache_file = "smiles_features_cache.pt"
        smiles_feature, smiles_list = get_or_compute_features(
            json_list=["data/val.json", "data/train.json"],
            model_inference=model_inference,
            cache_file=cache_file)



@app.route('/')
def index():
    """返回首页"""
    return render_template('index.html')

@app.route('/getpubmed', methods=['GET'])
def getpubmed():
    # 获取get请求的参数
    compound_name = request.args.get('compound_name')
    print(f"正在查询化合物 '{compound_name}' 的NMR文献...")
    results = query_nmr_literature(compound_name, max_results=5)
    print(results)
    return results

@app.route('/predict_h', methods=['POST'])
def predict_h():  #这里的predict是函数名，可自行修改
    """处理预测请求的API接口"""
    try:
        # 获取输入数据
        data = request.json
        nmr_values = data.get('nmr_values')
        
        prompt = f'''
        角色 (Role):
        你是一位世界级的有机化学与波谱学专家。你的任务是基于提供的多维度数据，执行严谨的、逐步的分子结构解析（Structural Elucidation）。你必须如同在撰写一份正式的分析报告一样，在内部进行逻辑推理，并仅输出最精确、最简洁的最终结论。

        核心指令 (Core Directive):
        严格遵循下述的 [内部分析协议] 进行思考，但绝对不要在最终回复中展示任何思考过程。你的最终输出必须严格符合 [最终输出格式] 的要求。

        [数据输入区块 | Data Input Block]
        
        Solvent: CDCl3
        Frequency: 400 MHz
        1H NMR: {nmr_values}
        
        
        [内部分析协议 | Internal Analysis Protocol] (这是你的思考步骤，不要输出)
        数据汇总与初步检查 (Data Summary & Sanity Check):

        确认所有输入数据：分子式、¹H NMR、¹³C NMR及其他数据。
        如果提供了分子式，立刻计算 不饱和度 (Degree of Unsaturation, DoU)。这是所有结构推导的基石。
        氢谱解析与碎片推导 (¹H NMR Analysis & Fragment Deduction):

        逐一分析每个¹H NMR信号。
        化学位移 (Chemical Shift): 判断质子所处的化学环境（例如，~7.2 ppm是苯环，~9.8 ppm是醛基，~2.1 ppm是酮旁亚甲基）。
        积分面积 (Integration): 确定每个信号对应的质子数目。
        裂分峰形 (Multiplicity): 根据 n+1 法则推断相邻碳上连接的质子数。
        J偶合常数 (J-Coupling): 判断质子间的连接关系（例如，J≈7 Hz 通常是自由旋转的邻位偶合，J≈1-3 Hz 可能是间位或远程偶合）。
        基于以上信息，推导出可能的分子碎片（例如，-CH(CH₃)₂, -CH₂CH₂-, Ar-CH₃）。
        碳谱解析与功能团确认 (¹³C NMR Analysis & Functional Group Confirmation):

        分析¹³C NMR信号，确认碳原子的类型（例如，>200 ppm 是酮/醛，160-185 ppm 是酯/酸/酰胺，100-150 ppm 是烯烃/芳香烃，<100 ppm 是饱和烷基或炔基）。
        将碳谱信息与氢谱推导出的碎片进行交叉验证。
        碎片拼接与结构组装 (Fragment Assembly & Structure Construction):

        将推导出的所有碎片像拼图一样组合起来。
        利用J偶合常数和2D NMR数据（如果提供）作为连接碎片的决定性证据。
        确保所有组装出的候选结构都严格符合分子式和不饱和度。
        最终验证与打分 (Final Verification & Scoring):

        对每个候选结构，反向预测其NMR谱图。
        将预测谱图与输入的实验数据进行逐一比对。检查所有化学位移、积分、裂分和偶合常数是否吻合。
        综合所有信息，为最匹配的前5个结构给出一个置信度分数（Confidence Score），分数越高代表匹配度越好
        [最终输出格式 | Final Output Format] (你的回复必须且只能是这个格式)

        禁止任何解释、引言或思考过程。直接提供一个带编号的列表，列表长度必须是5条，每条包含SMILES字符串和你的置信度分数。



        格式:
        编号. SMILES字符串 | 置信度分数(%)，保留二位小数，且置信度数值显示不能超过90%

        示例:
        O=CCCCCCCCC=O | 85.11%
        CC(C)C(=O)OCCCC | 88.22%
        CCCCCCCCC(=O)OC | 85.11%
        CCCCCC(=O)OCCC(C)C | 70.22%
        O=C(CCCCC)C(C)(C)C | 65.22%

        '''
        
        response = requests.post(
        url="https://openrouter.ai/api/v1/chat/completions",
        headers={
            "Authorization": "Bearer 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",
            "Content-Type": "application/json",
        },

        data=json.dumps({
            "model": "google/gemini-2.5-flash-preview-05-20",
            "messages": [
            {
                "role": "user",
                "content": [
                {
                    "type": "text",
                    "text": prompt
                },
                ]
            }
            ],
            
        })
        )

        results = response.json()['choices'][0]['message']['content']
        lines = results.strip().split('\n')
        lists = []
        for line in lines:
            parts = line.split('|')
            if len(parts) >= 2:
                smiles = parts[0].split()[-1]
                percent = parts[1].strip()
                # 将百分比转为小数形式（例如 89.25% -> 0.8925）
                decimal_percent = float(percent.strip('%')) / 100
                lists.append({"smiles": smiles, "percent": decimal_percent})

        print(lists)
        # 准备结果
        results = []
        # 循环lists中的每个字典,还有index
        for ii, item in enumerate(lists):
            # 提取smiles和percent
            smiles = item['smiles']
            percent = item['percent']
            # 提取百分比部分并转换为浮点数

            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                continue  # 跳过无效的SMILE S
            # RDKit属性
            formula = CalcMolFormula(mol)
            mol_weight = Descriptors.MolWt(mol)
            exact_mass = Descriptors.ExactMolWt(mol)
            # PubChemPy名称
            # name = get_iupac_from_pubchem(smiles)
            # 分子结构式图片生成为Base64
            img_io = io.BytesIO()
            img = Draw.MolToImage(mol, size=(300, 300))
            img.save(img_io, 'PNG')
            img_base64 = base64.b64encode(img_io.getvalue()).decode()
            
            
            results.append({
                "rank": ii + 1,
                "score": percent,
                # 'name': name,
                'smiles': smiles,
                'formula': formula,
                'mol_weight': round(mol_weight, 4),
                'exact_mass': round(exact_mass, 4),
                'img_base64': img_base64
            })
                        
                    
            # results.append({
            #     "rank": ii + 1,
            #     "score": float(score),
            #     "smiles": smiles,
            #     "formula": smiles_to_molecular_formula(smiles)
            # })
        
        #在这里加入大模型API接口调用
        # 返回结果
        return jsonify({
            "results": results,
            "search_time": '',
            "input_nmr": nmr_values
        })
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return jsonify({"error": str(e)}), 500


@app.route('/predict', methods=['POST'])
def predict():
    """处理预测请求的API接口"""
    try:
        # 确保模型已加载
        init_model()
        
        # 获取输入数据
        data = request.json
        nmr_values = data.get('nmr_values', [])
        
        # 验证输入
        if not nmr_values:
            return jsonify({"error": "请输入至少一个NMR值"}), 400
        
        # 转换为浮点数
        try:
            nmr_list = [float(x) for x in nmr_values]
        except ValueError:
            return jsonify({"error": "所有值必须为有效数字"}), 400
        
        # 提取NMR特征向量
        nmr_feature = model_inference.nmr_encode(nmr_list)
        
        # 使用FAISS搜索库获取top10候选
        start_time = time.time()
        indices, scores = get_topK_result_faiss(nmr_feature, smiles_feature, 5)
        search_time = time.time() - start_time
        
        # 准备结果
        results = []
        for ii, (idx, score) in enumerate(zip(indices[0], scores[0])):
            smiles = smiles_list[idx.item()]

            print(smiles)


            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                continue  # 跳过无效的SMILE S
            # RDKit属性
            formula = CalcMolFormula(mol)
            mol_weight = Descriptors.MolWt(mol)
            exact_mass = Descriptors.ExactMolWt(mol)
            # PubChemPy名称
            # name = get_iupac_from_pubchem(smiles)
            # 分子结构式图片生成为Base64
            img_io = io.BytesIO()
            img = Draw.MolToImage(mol, size=(300, 300))
            img.save(img_io, 'PNG')
            img_base64 = base64.b64encode(img_io.getvalue()).decode()
            
            
            results.append({
                "rank": ii + 1,
                "score": float(score),
                # 'name': name,
                'smiles': smiles,
                'formula': formula,
                'mol_weight': round(mol_weight, 4),
                'exact_mass': round(exact_mass, 4),
                'img_base64': img_base64
            })
                        
                    
            # results.append({
            #     "rank": ii + 1,
            #     "score": float(score),
            #     "smiles": smiles,
            #     "formula": smiles_to_molecular_formula(smiles)
            # })
        
        #在这里加入大模型API接口调用
        # 返回结果
        return jsonify({
            "results": results,
            "search_time": search_time,
            "input_nmr": nmr_list
        })
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/batch_predict', methods=['GET'])
def batch_predict(): 
    """批量预测接口"""
    try:
        init_model()
        data_dir = os.path.join(os.path.dirname(__file__), 'C13DATA')
        json_files = [f for f in os.listdir(data_dir) if f.endswith('.json')]
        
        total = 0
        correct = 0
        results = []

        for json_file in json_files:
            with open(os.path.join(data_dir, json_file), 'r') as f:
                data = json.load(f)
                smiles_truth = data['info'].get('SMILES')
                
                # 新增校验逻辑
                if not data.get('peaklist'):
                    results.append({
                        "file": json_file,
                        "error": "Missing peaklist field"
                    })
                    continue
                    
                if not smiles_truth:
                    results.append({
                        "file": json_file,
                        "error": "Missing SMILES field"
                    })
                    continue

                # 执行预测
                nmr_feature = model_inference.nmr_encode(data['peaklist'])
                indices, scores = get_topK_result_faiss(nmr_feature, smiles_feature, 10)
                
                # 检查预测结果
                predicted_smiles = [smiles_list[i.item()] for i in indices[0]]
                # 获取化学式真值
                chemical_formula_truth = data['info'].get('Chemical_Formula')
                
                # 新增校验逻辑
                if not chemical_formula_truth:
                    results.append({
                        "file": json_file,
                        "error": "Missing Chemical_Formula field"
                    })
                    continue

                # 执行预测并转换分子式
                predicted_formulas = []
                for s in predicted_smiles:
                    html_formula = smiles_to_molecular_formula(s)
                    # 移除HTML标签并标准化格式（匹配JSON中的格式）
                    plain_formula = re.sub(r'<sub>|</sub>', '', html_formula)
                    predicted_formulas.append(plain_formula)

                # 进行化学式比对
                is_correct = chemical_formula_truth in predicted_formulas

                results.append({
                    "file": json_file,
                    "truth": chemical_formula_truth,
                    "predicted": predicted_formulas,
                    "correct": is_correct
                })
                
                total += 1
                if is_correct:
                    correct += 1

        return jsonify({
            "accuracy": f"{correct/total:.1%}" if total > 0 else "N/A",
            "total_files": len(json_files),
            "valid_files": total,
            "correct_count": correct,
            "details": results
        })

    except Exception as e:
        print(f"Batch Error: {str(e)}")
        return jsonify({"error": str(e)}), 500

if __name__ == "__main__":
    app.run(debug=True, host='0.0.0.0', port=5000)