from flask import Flask, request, render_template, jsonify
import torch
import json
from tqdm import tqdm
import os
import time
import faiss
from infer import ModelInference
import re
from rdkit import Chem
from rdkit.Chem import rdMolDescriptors

from rdkit.Chem import Descriptors, Draw
from rdkit.Chem.rdMolDescriptors import CalcMolFormula
import io
import base64
import requests



app = Flask(__name__)

# 全局变量保存模型和特征，避免重复加载
model_inference = None
smiles_feature = None
smiles_list = None

def get_topK_result_faiss(nmr_feature, smiles_feature, topK):
    # 将特征转换为合适的格式
    smiles_feature_np = smiles_feature.cpu().numpy()
    nmr_feature_np = nmr_feature.cpu().numpy()
    
    # 创建FAISS索引
    dimension = smiles_feature_np.shape[1]
    index = faiss.IndexFlatIP(dimension)  # 使用内积相似度
    index.add(smiles_feature_np)
    
    # 搜索
    scores, indices = index.search(nmr_feature_np, topK)
    
    return torch.tensor(indices), torch.tensor(scores)

def get_or_compute_features(json_list, model_inference, cache_file="feature_cache.pt"):
    if os.path.exists(cache_file):
        print(f"Loading cached features from {cache_file}")
        return torch.load(cache_file)
    
    # 原有的特征提取代码
    smiles_feature, smiles_list = get_feature_from_json(
        json_list=json_list,
        model_inference=model_inference,
        n=256,  # 增大批量大小
        save_name=None,
        type="smiles",
        flag_get_value=True)
    
    # 缓存结果
    torch.save((smiles_feature, smiles_list), cache_file)
    return smiles_feature, smiles_list

def get_feature_from_json(json_list,
                          save_name,
                          model_inference,
                          n=512,
                          type="nmr",
                          flag_get_value=False):
    context = []
    print("start parse json")
    for file in json_list:
        with open(file, "r") as f:
            context_tmp = json.loads(f.read())
            context_tmp = [
                i[type][0] for i in tqdm(context_tmp) if len(i[type]) > 0
            ]
        context += context_tmp
    print("Size of the library: ", len(context))
    if flag_get_value == "only":
        return context
    if type == "nmr":
        fn = model_inference.nmr_encode
    elif type == "smiles":
        fn = model_inference.smiles_encode
    contexts = []
    print("start load batch")
    for i in range(0, len(context), n):
        contexts.append(context[i:i + n])
    print("start encode batch")
    result = [fn(i).cpu() for i in tqdm(contexts)]
    result = torch.cat(result, 0)
    if flag_get_value is True:
        if save_name is not None:
            torch.save((result, context), save_name)
        return result, context

    if save_name is not None:
        torch.save(result, save_name)
    return result

def smiles_to_molecular_formula(smiles):
    print("输入的SMILES:", smiles)
    """将SMILES转换为分子式"""
    try:
        mol = Chem.MolFromSmiles(smiles)
        if mol is None:
            return "转换失败"
        
        # 获取分子式
        formula = rdMolDescriptors.CalcMolFormula(mol)
        
        # 转换为带下标的形式 (HTML格式)
        formula_html = re.sub(r'(\d+)', r'<sub>\1</sub>', formula)
        
        return formula_html
    except Exception as e:
        print(f"转换SMILES到分子式时出错: {e}")
        return "转换失败"

def get_iupac_from_pubchem(smiles):
    url = f"https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/smiles/{smiles}/property/IUPACName/JSON"
    response = requests.get(url, timeout=10, verify=False)  # 不建议生产环境长用 verify=False
    if response.ok:
        try:
            data = response.json()
            name = data['PropertyTable']['Properties'][0].get('IUPACName', '名称不可用')
            return name
        except Exception as e:
            print("解析JSON失败：", e)
            return "解析失败"
    else:
        return "名称不可用"


def init_model():
    """初始化模型和特征，在第一次请求时调用"""
    global model_inference, smiles_feature, smiles_list
    
    if model_inference is None:
        # 设置环境变量
        os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"
        
        # 加载模型
        config_path = "models/2_5_w_model/8.json"
        pretrain_model_path = "models/2_5_w_model/8.pth"
        model_inference = ModelInference(config_path=config_path,
                                         pretrain_model_path=pretrain_model_path,
                                         device="cuda")
        
        # 加载特征
        cache_file = "smiles_features_cache.pt"
        smiles_feature, smiles_list = get_or_compute_features(
            json_list=["data/val.json", "data/train.json"],
            model_inference=model_inference,
            cache_file=cache_file)



@app.route('/')
def index():
    """返回首页"""
    return render_template('index.html')

@app.route('/predict', methods=['POST'])
def predict():
    """处理预测请求的API接口"""
    try:
        # 确保模型已加载
        init_model()
        
        # 获取输入数据
        data = request.json
        nmr_values = data.get('nmr_values', [])
        
        # 验证输入
        if not nmr_values:
            return jsonify({"error": "请输入至少一个NMR值"}), 400
        
        # 转换为浮点数
        try:
            nmr_list = [float(x) for x in nmr_values]
        except ValueError:
            return jsonify({"error": "所有值必须为有效数字"}), 400
        
        # 提取NMR特征向量
        nmr_feature = model_inference.nmr_encode(nmr_list)
        
        # 使用FAISS搜索库获取top10候选
        start_time = time.time()
        indices, scores = get_topK_result_faiss(nmr_feature, smiles_feature, 10)
        search_time = time.time() - start_time
        
        # 准备结果
        results = []
        for ii, (idx, score) in enumerate(zip(indices[0], scores[0])):
            smiles = smiles_list[idx.item()]

            print(smiles)


            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                continue  # 跳过无效的SMILE S
            # RDKit属性
            formula = CalcMolFormula(mol)
            mol_weight = Descriptors.MolWt(mol)
            exact_mass = Descriptors.ExactMolWt(mol)
            # PubChemPy名称
            # name = get_iupac_from_pubchem(smiles)
            # 分子结构式图片生成为Base64
            img_io = io.BytesIO()
            img = Draw.MolToImage(mol, size=(300, 300))
            img.save(img_io, 'PNG')
            img_base64 = base64.b64encode(img_io.getvalue()).decode()
            
            
            results.append({
                "rank": ii + 1,
                "score": float(score),
                # 'name': name,
                'smiles': smiles,
                'formula': formula,
                'mol_weight': round(mol_weight, 4),
                'exact_mass': round(exact_mass, 4),
                'img_base64': img_base64
            })
                        
                    
            # results.append({
            #     "rank": ii + 1,
            #     "score": float(score),
            #     "smiles": smiles,
            #     "formula": smiles_to_molecular_formula(smiles)
            # })
        
        # 返回结果
        return jsonify({
            "results": results,
            "search_time": search_time,
            "input_nmr": nmr_list
        })
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/batch_predict', methods=['GET'])
def batch_predict(): 
    """批量预测接口"""
    try:
        init_model()
        data_dir = os.path.join(os.path.dirname(__file__), 'C13DATA')
        json_files = [f for f in os.listdir(data_dir) if f.endswith('.json')]
        
        total = 0
        correct = 0
        results = []

        for json_file in json_files:
            with open(os.path.join(data_dir, json_file), 'r') as f:
                data = json.load(f)
                smiles_truth = data['info'].get('SMILES')
                
                # 新增校验逻辑
                if not data.get('peaklist'):
                    results.append({
                        "file": json_file,
                        "error": "Missing peaklist field"
                    })
                    continue
                    
                if not smiles_truth:
                    results.append({
                        "file": json_file,
                        "error": "Missing SMILES field"
                    })
                    continue

                # 执行预测
                nmr_feature = model_inference.nmr_encode(data['peaklist'])
                indices, scores = get_topK_result_faiss(nmr_feature, smiles_feature, 10)
                
                # 检查预测结果
                predicted_smiles = [smiles_list[i.item()] for i in indices[0]]
                # 获取化学式真值
                chemical_formula_truth = data['info'].get('Chemical_Formula')
                
                # 新增校验逻辑
                if not chemical_formula_truth:
                    results.append({
                        "file": json_file,
                        "error": "Missing Chemical_Formula field"
                    })
                    continue

                # 执行预测并转换分子式
                predicted_formulas = []
                for s in predicted_smiles:
                    html_formula = smiles_to_molecular_formula(s)
                    # 移除HTML标签并标准化格式（匹配JSON中的格式）
                    plain_formula = re.sub(r'<sub>|</sub>', '', html_formula)
                    predicted_formulas.append(plain_formula)

                # 进行化学式比对
                is_correct = chemical_formula_truth in predicted_formulas

                results.append({
                    "file": json_file,
                    "truth": chemical_formula_truth,
                    "predicted": predicted_formulas,
                    "correct": is_correct
                })
                
                total += 1
                if is_correct:
                    correct += 1

        return jsonify({
            "accuracy": f"{correct/total:.1%}" if total > 0 else "N/A",
            "total_files": len(json_files),
            "valid_files": total,
            "correct_count": correct,
            "details": results
        })

    except Exception as e:
        print(f"Batch Error: {str(e)}")
        return jsonify({"error": str(e)}), 500

if __name__ == "__main__":
    app.run(debug=True, host='0.0.0.0', port=5000)